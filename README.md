# LLA Web - Full Stack CMS Architecture

A modern full-stack web application built with SvelteKit frontend and Rust CMS backend, deployed on Cloudflare's edge infrastructure.

## Architecture Overview

### Frontend
- **SvelteKit** deployed to **Cloudflare Pages** with SSR
- Routes: Static pages (prerendered), blog (SSR with KV), portal (SSR with auth)
- Domains: `llacademy.ng`, `www.llacademy.ng`

### Backend
- **Rust CMS** deployed on VPS via Dokploy
- Serves APIs at `school.llacademy.ng`
- Handles authentication, blog management, file uploads

### Storage & Services (Cloudflare)
- **KV Store** (`BLOG_KV`): Blog posts and index
- **D1 Database** (`school_auth`): Users, sessions, audit logs
- **Cloudflare Images**: Image optimization and CDN
- **R2 Storage** (`school-media`): Large file storage
- **CDN**: `media.llacademy.ng` for assets

## Project Structure

```
.
├── src/                          # SvelteKit frontend
│   ├── routes/
│   │   ├── (home)/              # Public homepage
│   │   ├── blog/                # Blog pages (SSR)
│   │   ├── portal/              # Authenticated user portal
│   │   │   ├── admin/           # Admin interface
│   │   │   └── posts/           # Blog management
│   │   └── login/               # Authentication
│   ├── hooks.server.ts          # Auth & caching middleware
│   └── app.d.ts                 # TypeScript definitions
├── cms/                         # Rust CMS backend
│   ├── src/
│   │   ├── main.rs             # Application entry point
│   │   ├── handlers.rs         # API route handlers
│   │   ├── auth.rs             # Authentication logic
│   │   ├── blog.rs             # Blog management
│   │   └── models.rs           # Data models
│   └── migrations/             # Database migrations
├── wrangler.toml               # Cloudflare configuration
├── svelte.config.js            # SvelteKit configuration
└── .github/workflows/          # CI/CD pipelines
```

## Features

### Public Features
- **Homepage**: Company information, services, portfolio
- **Blog**: Public blog posts with SSR and edge caching
- **Static Pages**: About, Contact, Admissions, etc.

### Authenticated Features
- **Portal Dashboard**: User-specific dashboard
- **Admin Interface**: Blog post management (admin only)
- **Authentication**: Secure login with session management

### Technical Features
- **SSR with Caching**: Blog pages use SSR with edge caching
- **Authentication**: Cookie-based sessions with D1 storage
- **File Uploads**: Images to Cloudflare Images, files to R2
- **Edge Optimization**: Cloudflare CDN with cache control
- **Security**: CORS, CSRF protection, secure headers

## Development Setup

### Prerequisites
- Node.js 18+
- Rust 1.70+
- Cloudflare account with:
  - KV namespace
  - D1 database
  - R2 bucket
  - Images service

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/bezaleel22/llaweb.git
   cd llaweb
   ```

2. **Install frontend dependencies**
   ```bash
   npm install
   ```

3. **Setup Rust CMS**
   ```bash
   cd cms
   cargo build
   ```

4. **Configure Cloudflare**
   - Update `wrangler.toml` with your namespace/database IDs
   - Set up environment variables in Cloudflare dashboard

5. **Database Setup**
   ```bash
   cd cms
   wrangler d1 migrations apply school_auth --local
   ```

### Development Commands

**Frontend Development**
```bash
npm run dev          # Start SvelteKit dev server
npm run build        # Build for production
npm run preview      # Preview production build
```

**Backend Development**
```bash
cd cms
cargo run            # Start Rust CMS server
cargo test           # Run tests
```

**Database Management**
```bash
cd cms
wrangler d1 migrations create <name>     # Create new migration
wrangler d1 migrations apply --local     # Apply migrations locally
wrangler d1 migrations apply --remote    # Apply migrations to production
```

## Deployment

### Automatic Deployment (Recommended)

The project includes GitHub Actions workflows for automatic deployment:

1. **Frontend**: Automatically deploys to Cloudflare Pages on push to `main`
2. **Backend**: Deploys Rust CMS to VPS via SSH
3. **Database**: Runs D1 migrations

Required GitHub Secrets:
- `CLOUDFLARE_API_TOKEN`
- `CLOUDFLARE_ACCOUNT_ID`
- `VPS_HOST`
- `VPS_USERNAME`
- `VPS_SSH_KEY`

### Manual Deployment

**Frontend to Cloudflare Pages**
```bash
npm run build
wrangler pages deploy .svelte-kit/cloudflare
```

**Backend to VPS**
```bash
cd cms
cargo build --release
# Copy binary to VPS and restart service
```

## Data Models

### KV Store (BLOG_KV)
```javascript
// Blog post: key = "blog:post:{slug}"
{
  "id": "uuid",
  "title": "Post Title",
  "slug": "post-slug",
  "summary": "Brief summary",
  "body_html": "<p>HTML content</p>",
  "author_id": "uuid",
  "tags": ["tag1", "tag2"],
  "date_published": "2025-01-01T12:00:00Z",
  "visibility": "public|private",
  "cover_image": "image_url",
  "attachments": ["file_urls"]
}

// Blog index: key = "blog:index"
[
  {
    "slug": "post-slug",
    "title": "Post Title",
    "summary": "Brief summary",
    "cover_image": "image_url",
    "date_published": "2025-01-01T12:00:00Z",
    "tags": ["tag1", "tag2"]
  }
]
```

### D1 Database Tables

**users**
- `id` (TEXT PRIMARY KEY)
- `email` (TEXT UNIQUE)
- `password_hash` (TEXT)
- `role` (TEXT: 'admin' | 'user')
- `full_name` (TEXT)
- `created_at` (TEXT)
- `updated_at` (TEXT)

**sessions**
- `id` (TEXT PRIMARY KEY)
- `user_id` (TEXT)
- `jwt_token` (TEXT)
- `expires_at` (TEXT)
- `created_at` (TEXT)

**audit_logs**
- `id` (TEXT PRIMARY KEY)
- `user_id` (TEXT)
- `action` (TEXT)
- `resource_id` (TEXT)
- `details` (TEXT)
- `created_at` (TEXT)

## API Endpoints

### Public Endpoints
- `GET /api/blog/index` - Get public blog posts
- `GET /api/blog/post/{slug}` - Get public blog post
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /healthz` - Health check

### Protected Endpoints (Admin)
- `GET /api/admin/posts` - List all posts (including private)
- `POST /api/admin/posts` - Create new post
- `GET /api/admin/posts/{slug}` - Get specific post
- `PUT /api/admin/posts/{slug}` - Update post
- `DELETE /api/admin/posts/{slug}` - Delete post
- `GET /api/users/me` - Get current user info

## Security

### Authentication
- Cookie-based sessions with HttpOnly, Secure, SameSite attributes
- Session tokens stored in D1 with expiration
- CORS configured for specific domains
- Admin role verification for sensitive operations

### Headers
- Content Security Policy
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- X-XSS-Protection
- Strict-Transport-Security (via Cloudflare)

### Caching Strategy
- **Public content**: `Cache-Control: public, max-age=60, s-maxage=3600`
- **Private content**: `Cache-Control: private, no-store`
- **Admin areas**: No caching

## Performance

### Caching
- **Edge Caching**: Public blog posts cached at Cloudflare edge
- **KV Performance**: Sub-50ms read latency globally
- **Image Optimization**: Cloudflare Images with automatic WebP/AVIF
- **Static Assets**: Aggressive caching via Cloudflare CDN

### Monitoring
- Health checks on `/healthz` endpoint
- Error logging with structured JSON
- Performance metrics via Cloudflare Analytics

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature`
3. Make your changes
4. Run tests: `cargo test` (backend) and `npm test` (frontend)
5. Commit your changes: `git commit -m 'Add your feature'`
6. Push to the branch: `git push origin feature/your-feature`
7. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For questions or support, please contact the development team or create an issue in the GitHub repository.
