export const articles = [
  {
    id: 1,
    title: 'Getting Started with SvelteKit',
    content: `
            Getting Started with SvelteKit
            SvelteKit is a modern framework for building web applications...

            Introduction
            SvelteKit offers a seamless way to create Svelte applications...`,
    link: 'article/1'
  },
  {
    id: 2,
    title: 'Markdown with SvelteKit',
    content: `
# Markdown with SvelteKit
With SvelteKit, integrating Markdown is made simple through the mdsvex plugin.

## Benefits of mdsvex
- Allows you to write Markdown inside your Svelte components.
- Supports frontmatter and Svelte components within Markdown.
    `,
  link: 'article/2'
  },
  ]
  