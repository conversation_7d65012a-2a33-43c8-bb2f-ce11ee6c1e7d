@import 'https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&amp;display=swap';
@import 'https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&amp;display=swap';

body {
    margin: 0;
    padding: 0
}

body {
    font-family: roboto, sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0;
    color: #74777c;
    overflow-y: scroll;
    overflow-x: hidden;
    overflow: auto;
}

::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background-color: #ebebeb;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    -webkit-border-radius: 10px;
    border-radius: 10px;
    background: #6d6d6d56;
}

p {
    margin: 0 0 15px
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: poppins, sans-serif;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1.5;
    margin: 0 0 15px
}

.headFont {
    font-family: poppins, sans-serif
}

:root {
    --theme-color: #ed1b24
}

a {
    color: #1a1a1a;
    text-decoration: none;
    transition: all ease 400ms;
    -moz-transition: all ease 400ms;
    -webkit-transition: all ease 400ms
}

a:hover {
    color: var(--theme-color);
    text-decoration: none
}

input:focus,
button:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 0;
    box-shadow: none
}

input[type=search]::-webkit-search-decoration,
input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-results-button,
input[type=search]::-webkit-search-results-decoration {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none
}

input[type=tel]::-webkit-outer-spin-button,
input[type=tel]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0
}

.subTitle {
    font-weight: 500;
    font-size: 18px;
    line-height: .9;
    color: var(--theme-color);
    margin: 0 0 24px
}

.subTitle span {
    display: inline-block;
    width: 50px;
    height: 2px;
    background: var(--theme-color);
    margin-right: 20px;
    position: relative;
    top: -5px
}

.subTitle .bright {
    margin-right: 0;
    margin-left: 20px
}

.secTitle {
    font-size: 60px;
    line-height: 65px;
    margin: 0 0 63px
}

.white {
    color: #fff
}

.secTitle span {
    color: var(--theme-color)
}

.secDesc {
    line-height: 28px;
    margin: 0 0 36px
}

.qu_btn {
    position: relative;
    z-index: 2;
    overflow: hidden;
    height: 60px;
    border: none;
    background: linear-gradient(90deg, rgba(237, 28, 36, 1) 0%, rgba(237, 82, 28, 1) 100%);
    border-radius: 50px;
    padding: 0 36px;
    text-align: center;
    display: inline-block;
    font-size: 16px;
    text-transform: capitalize;
    color: #fff;
    font-weight: 700;
    line-height: 60px;
    transition-delay: 0s, 0s;
    transition-duration: .4s, .4s;
    transition-property: border-color, color;
    transition-timing-function: cubic-bezier(.2, 1, .3, 1);
    vertical-align: middle
}

.qu_btn:hover {
    color: #fff
}

.qu_btn:before {
    background: #1a1a1a;
    content: "";
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    transform: rotate3d(0, 0, 1, -45deg) translate3d(0px, -3em, 0px);
    transform-origin: 0 100% 0;
    transition: transform .4s ease 0s, opacity .4s ease 0s, background-color .4s ease 0s;
    width: 150%;
    z-index: -1
}

.qu_btn:hover:before {
    opacity: 1;
    transform: rotate3d(0, 0, 1, 0deg);
    transition-timing-function: cubic-bezier(.3, 1, .3, 1)
}

.qu_link {
    position: relative;
    font-size: 18px;
    line-height: 24px;
    color: #1a1a1a;
    font-weight: 500;
    margin: 0
}

.qu_link a {
    display: inline-block;
    color: var(--theme-color);
    border-bottom: 1px solid var(--theme-color)
}

.qu_link a:hover {
    color: #1a1a1a;
    border-color: transparent
}

.noPadding {
    padding: 0
}

.noPaddingLeft {
    padding-left: 0
}

.noPaddingRight {
    padding-right: 0
}

.noPaddingTop {
    padding-top: 0
}

@media(min-width:1440px) {
    .container.largeContainer {
        max-width: 1250px
    }
}

.SecLayerimg {
    position: absolute;
    left: 0;
    top: 0
}

.blinker_anim img,
.SecLayerimg img {
    max-width: 100%;
    height: auto
}

.move_anim img {
    -webkit-animation: movebounce 3s linear infinite;
    animation: movebounce 3s linear infinite
}

.move_anim2 img {
    -webkit-animation: movebounce2 3s linear infinite;
    animation: movebounce2 3s linear infinite
}

.blinker_anim img {
    -webkit-animation: blinker 3s infinite linear;
    animation: blinker 3s infinite linear
}

.blinker_anim2 img {
    -webkit-animation: blinker2 3s infinite linear;
    animation: blinker2 3s infinite linear
}

.rotate_anim img {
    -webkit-animation: rotated 3s infinite linear;
    animation: rotated 3s infinite linear
}

.shine-anim {
    overflow: hidden
}

.shine-anim:before {
    position: absolute;
    top: 0;
    left: -85%;
    z-index: 2;
    display: block;
    content: '';
    width: 50%;
    height: 100%;
    background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 100%);
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 100%);
    -webkit-transform: skewX(-25deg);
    transform: skewX(-25deg)
}

.shine-anim:hover:before {
    -webkit-animation: shine 1s;
    animation: shine 1s
}

@keyframes movebounce {
    0% {
        transform: translateY(0px)
    }

    50% {
        transform: translateY(20px)
    }

    100% {
        transform: translateY(0px)
    }
}

@keyframes movebounce2 {
    0% {
        transform: translateX(0px)
    }

    50% {
        transform: translateX(20px)
    }

    100% {
        transform: translateX(0px)
    }
}

@-webkit-keyframes rotated {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes blinker {
    0% {
        transform: scale(1, 1)
    }

    50% {
        transform: scale(1.03, 1.03)
    }

    100% {
        transform: scale(1, 1)
    }
}

@keyframes blinker2 {
    0% {
        transform: scale(1, 1)
    }

    50% {
        transform: scale(1.5, 1.5)
    }

    100% {
        transform: scale(1, 1)
    }
}

@keyframes circle {
    0% {
        transform: rotate(90deg)
    }

    100% {
        transform: rotate(450deg)
    }
}

@keyframes shadows {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, .15), 0 0 0 30px rgba(255, 255, 255, .15), 0 0 0 50px rgba(255, 255, 255, .15)
    }

    100% {
        box-shadow: 0 0 0 30px rgba(255, 255, 255, .15), 0 0 0 50px rgba(255, 255, 255, .15), 0 0 0 70px transparent
    }
}

@-webkit-keyframes shine {
    100% {
        left: 125%
    }
}

@keyframes shine {
    100% {
        left: 125%
    }
}

@-webkit-keyframes zoom-hover {
    0% {
        opacity: 1
    }

    40% {
        opacity: 1
    }

    100% {
        width: 200%;
        height: 200%;
        opacity: 0
    }
}

.overlay-anim::before {
    background: rgba(255, 255, 255, .3);
    bottom: 0;
    content: "";
    left: 50%;
    position: absolute;
    right: 51%;
    top: 0;
    opacity: 1;
    pointer-events: none;
    transition: all 500ms linear
}

.overlay-anim:hover::before {
    left: 0;
    right: 0;
    opacity: 0;
    transition: all 500ms linear
}

.overlay-anim::after {
    background: rgba(255, 255, 255, .3);
    bottom: 50%;
    content: "";
    left: 0;
    position: absolute;
    right: 0;
    top: 50%;
    opacity: 1;
    pointer-events: none;
    transition: all 600ms linear
}

.overlay-anim:hover::after {
    top: 0;
    bottom: 0;
    opacity: 0;
    transition: all 600ms linear
}

@keyframes zoomBig {
    0% {
        transform: translate(-50%, -50%) scale(.5);
        opacity: 1;
        border-width: 3px
    }

    40% {
        opacity: .5;
        border-width: 2px
    }

    65% {
        border-width: 1px
    }

    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0;
        border-width: 1px
    }
}

#backtotop {
    position: fixed;
    right: 25px;
    bottom: -25px;
    width: 40px;
    height: 40px;
    background: var(--theme-color);
    color: #fff;
    font-size: 16px;
    text-align: center;
    line-height: 40px;
    padding: 0;
    opacity: 0;
    visibility: hidden;
    z-index: 999;
    border-radius: 50%
}

#backtotop i {
    line-height: inherit
}

#backtotop:hover {
    background: var(--theme-color)
}

.fixedHeader.header02 {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    background: #fff;
    z-index: 99;
    box-shadow: 0 0 25px rgba(0, 0, 0, .15)
}

.fixedHeader.header03 {
    position: fixed;
    width: 100%;
    padding: 0 100px;
    border: none;
    top: 0;
    left: 0;
    background: #1a1a1a;
    z-index: 99;
    box-shadow: 0 0 25px rgba(255, 255, 255, .15)
}

.fixedHeader.header03 .mainMenu>ul>li {
    padding: 39px 0
}

.fixedHeader.header03 .logo {
    padding: 14px 0
}

.preloader {
    background: #1a1a1a;
    bottom: 0;
    height: 100%;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    width: 100%;
    z-index: 99999
}

@keyframes rotate {
    100% {
        transform: rotate(360deg)
    }
}

.queraLoader {
    width: 209px;
    height: 200px;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto
}

.queraLoader:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: url(/images/loader2.png) no-repeat center center;
    opacity: .7;
    animation: rotate 15s infinite linear
}

.loaderO {
    position: absolute;
    top: 52%;
    left: 0;
    right: 0;
    margin: 0 auto;
    transform: translateY(-50%);
    z-index: 1;
    line-height: 18px
}

.loaderO span {
    font-family: spartan, sans-serif;
    font-size: 14px;
    line-height: 14px;
    font-weight: 700;
    color: #f0f8ff;
    display: inline-block;
    transition: all .5s;
    animation: animate 2s infinite
}

.loaderO span:nth-child(1) {
    animation-delay: .1s
}

.loaderO span:nth-child(2) {
    animation-delay: .2s
}

.loaderO span:nth-child(3) {
    animation-delay: .3s
}

.loaderO span:nth-child(4) {
    animation-delay: .4s
}

.loaderO span:nth-child(5) {
    animation-delay: .5s
}

.loaderO span:nth-child(6) {
    animation-delay: .6s
}

.loaderO span:nth-child(7) {
    animation-delay: .7s
}

@keyframes animate {
    0% {
        color: #f0f8ff;
        transform: translateY(0);
        margin-left: 0
    }

    25% {
        color: #f0f8ff;
        transform: translateY(-15px);
        margin-left: 5px;
        text-shadow: 0 15px 5px #000
    }

    100% {
        color: #f0f8ff;
        transform: translateY(0);
        margin-left: 0
    }
}