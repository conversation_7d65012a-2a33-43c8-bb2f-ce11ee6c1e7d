<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="%sveltekit.assets%/favicon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />

  <!-- Preload critical resources -->
  <link rel="preload" href="%sveltekit.assets%/js/jquery.js" as="script" />
  <link rel="preload" href="%sveltekit.assets%/js/bootstrap.min.js" as="script" />

  <!-- DNS prefetch for external resources -->
  <link rel="dns-prefetch" href="//fonts.googleapis.com" />

  %sveltekit.head%
</head>

<body data-sveltekit-preload-data="hover">
  <div style="display: contents">%sveltekit.body%</div>
</body>
<script src="%sveltekit.assets%/js/jquery.js"></script>
<script src="%sveltekit.assets%/js/bootstrap.min.js"></script>
<script src="%sveltekit.assets%/js/jquery.appear.js"></script>
<script src="%sveltekit.assets%/js/owl.carousel.min.js"></script>
<script src="%sveltekit.assets%/js/shuffle.min.js"></script>
<script src="%sveltekit.assets%/js/nice-select.js"></script>
<script src="%sveltekit.assets%/js/lightcase.js"></script>
<script src="%sveltekit.assets%/js/jquery.datetimepicker.full.min.js"></script>
<script src="%sveltekit.assets%/js/circle-progress.js"></script>
<script src="%sveltekit.assets%/js/gmaps.js"></script>
<!-- <script
    src="https://maps.google.com/maps/api/js?key=AIzaSyBJtPMZ_LWZKuHTLq5o08KSncQufIhPU3o"
  ></script> -->

<script src="%sveltekit.assets%/js/jquery.themepunch.tools.min.js"></script>
<script src="%sveltekit.assets%/js/jquery.themepunch.revolution.min.js"></script>

<script src="%sveltekit.assets%/js/extensions/revolution.extension.actions.min.js"></script>
<script src="%sveltekit.assets%/js/extensions/revolution.extension.carousel.min.js"></script>
<script src="%sveltekit.assets%/js/extensions/revolution.extension.kenburn.min.js"></script>
<script src="%sveltekit.assets%/js/extensions/revolution.extension.migration.min.js"></script>
<script src="%sveltekit.assets%/js/extensions/revolution.extension.navigation.min.js"></script>
<script src="%sveltekit.assets%/js/extensions/revolution.extension.parallax.min.js"></script>
<script src="%sveltekit.assets%/js/extensions/revolution.extension.slideanims.min.js"></script>
<script src="%sveltekit.assets%/js/extensions/revolution.extension.video.min.js"></script>
<script src="%sveltekit.assets%/js/extensions/revolution.extension.layeranimation.min.js"></script>
<script src="%sveltekit.assets%/js/theme.js"></script>

</html>