Perfect — here’s the updated **Change Request Ticket** with **SQLite on NVMe**, **WAL mode**, **incremental JSON audit logging**, and the optimizations/mitigations we discussed.

---

# 🎟️ **Change Request Ticket**

## Title

Migrate from Cloudflare D1 to Local SQLite with NVMe WAL Optimizations & Incremental JSON Audit Logging

---

## Background

The current backend design included Cloudflare D1 for structured data. However, D1 free-tier limits on reads/writes and audit log overhead create long-term constraints.

The school’s Rust CMS already runs on a VPS with **NVMe storage (10 GB allocated)**, **2 vCPU**, and **1 GB RAM**. SQLite with **WAL mode** is a better fit:

* Leverages NVMe sequential writes.
* Avoids D1 quota limits.
* Simplifies architecture.
* Provides full control over audit logging strategy.

Backups will be pushed to **MS365 SharePoint Drive** (already provisioned) with optional R2 redundancy.

---

## Scope of Change

### 1. **Database Setup**

* Deploy SQLite locally in **WAL mode**.
* Apply tuning pragmas:

  * `PRAGMA journal_mode = WAL;`
  * `PRAGMA synchronous = NORMAL;`
  * `PRAGMA cache_size = -65536;` (64MB page cache)
  * `PRAGMA temp_store = MEMORY;`
* Indexes:

  * `users.email`
  * `users.google_id`
  * `revocations.jti`
  * `audit_logs.user_id`

---

### 2. **Data Model (New Auth Model)**

**Users & Revocations**

```sql
users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  role TEXT CHECK(role IN ('student','parent','teacher','admin')),
  google_id TEXT UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)

revocations (
  jti TEXT PRIMARY KEY,
  user_id TEXT,
  revoked_at TIMESTAMP,
  expires_at TIMESTAMP
)
```

* Authentication: **passwordless login + Google Auth**.
* JWT with **revocation list** (rare updates, negligible overhead).
* No session table (avoids constant DB reads).

---

### 3. **Audit Log Optimization**

**Incremental JSON Appends**

* Each session/day has 1 JSON blob in SQLite.
* Actions appended with `json_insert()` → durable immediately in WAL file.
* Negligible memory usage (no large in-RAM buffers).

**Row Rotation Mitigation**

* If `actions` JSON exceeds 1 MB or 50 actions → start a new row.
* Prevents costly updates to very large JSON arrays.

**Monthly Sharding**

* Separate tables by month (`audit_logs_2025_09`, `audit_logs_2025_10`).
* Keeps queries fast & cleanup simple.

**Archival**

* Keep last 3 months in SQLite.
* Older logs exported to SharePoint Drive.
* Optional R2 redundancy.

---

### 4. **Backup Strategy**

* Daily SQLite `.backup` run.
* Upload backups to **SharePoint Drive** via MS Graph API.
* Retention:

  * 30 days in SharePoint.
  * Monthly archive in R2 (optional).

---

## Risks & Mitigation

1. **Single Writer Limitation**

   * WAL mode supports concurrent readers + one writer.
   * Writes are small, sequential, and NVMe-optimized.
   * Row rotation avoids “giant JSON row” contention.

2. **DB Corruption Risk**

   * Mitigation: daily backups to SharePoint.
   * NVMe storage + WAL reduces corruption likelihood.

3. **Scaling Beyond VPS**

   * SQLite can handle **tens of thousands of DAU** on NVMe.
   * Migration path: Postgres/MySQL if school exceeds \~50k active users.

4. **Querying JSON Logs**

   * JSON queries heavier than row-per-action.
   * Mitigation: use JSON1 extension for analytics; operational queries remain light.

---

## Acceptance Criteria

* [ ] SQLite deployed with WAL + tuned pragmas.
* [ ] New data model applied (`users`, `revocations`, sharded `audit_logs`).
* [ ] Audit log strategy implemented: incremental JSON append + row rotation.
* [ ] Daily SharePoint backup verified.
* [ ] Documentation updated (DB tuning, log querying, backup recovery).

---

## Timeline

* **Week 1**: Setup SQLite with WAL & schema.
* **Week 2**: Implement audit log append + row rotation logic.
* **Week 3**: Configure monthly sharding & SharePoint backup integration.
* **Week 4**: Final testing, documentation, and rollout.

---

✅ This change eliminates D1 bottlenecks, maximizes NVMe performance, and ensures long-term audit compliance with efficient storage + backup.

---

Would you like me to also **add a growth forecast table** (users, actions/day, audit log DB size over 1 year) so you can show stakeholders how this scales on your current VPS?
