mod models;
mod auth;
mod blog;
mod storage;
mod handlers;
mod config;
mod error;
mod kv;
mod middleware;

use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use crate::config::AppConfig;
use crate::error::AppResult;
use crate::kv::KvStore;
use sqlx::{SqlitePool, QueryBuilder};

#[derive(Clone)]
pub struct AppState {
    pub db: SqlitePool,
    pub config: AppConfig,
    pub kv: KvStore,
}

impl AppState {
    pub fn new(db: SqlitePool, config: AppConfig, kv: KvStore) -> Self {
        Self { db, config, kv }
    }
}

#[tokio::main]
async fn main() -> AppResult<()> {
    // Initialize tracing
    tracing_subscriber::registry()
        .with(tracing_subscriber::EnvFilter::new(
            std::env::var("RUST_LOG").unwrap_or_else(|_| "info".into()),
        ))
        .with(tracing_subscriber::fmt::layer())
        .init();

    // Load configuration
    let config = AppConfig::from_env()?;
    tracing::info!("Loaded configuration for environment: {}", config.environment);

    // Initialize database
    let db = SqlitePool::connect(&config.database_url).await?;
    tracing::info!("Connected to database");

    // Run migrations
    sqlx::migrate!("./migrations").run(&db).await?;
    tracing::info!("Database migrations completed");

    // Initialize KV store with proper storage directory
    let kv_storage_dir = if config.environment == "development" {
        "kv_storage"
    } else {
        "/tmp/kv_storage" // For production container
    };
    let kv = KvStore::new(kv_storage_dir)?;
    tracing::info!("KV store initialized for environment: {}", config.environment);

    // Initialize application state
    let state = AppState::new(db.clone(), config.clone(), kv);

    // Initialize default admin user if in development
    if config.environment == "development" {
        initialize_default_admin(&db).await?;
    }

    // Create router
    let app = handlers::create_router(state);

    // Start server
    let addr = format!("0.0.0.0:{}", config.server_port);
    let listener = tokio::net::TcpListener::bind(&addr).await?;
    tracing::info!("Server running on http://{}", addr);

    axum::serve(listener, app).await?;

    Ok(())
}

async fn initialize_default_admin(db: &SqlitePool) -> AppResult<()> {
    use crate::models::UserRole;

    // Check if admin user already exists
    let existing_admin: Option<String> = sqlx::query_scalar("SELECT id FROM users WHERE email = ?")
        .bind("<EMAIL>")
        .fetch_optional(db)
        .await?;

    if existing_admin.is_some() {
        tracing::info!("Default admin user already exists");
        return Ok(());
    }

    // Create default admin user
    let password_hash = bcrypt::hash("admin123", bcrypt::DEFAULT_COST)?;
    let admin_user = models::User::new(
        "<EMAIL>".to_string(),
        password_hash,
        UserRole::Admin,
        "Administrator".to_string(),
    );

    sqlx::query("INSERT INTO users (id, email, password_hash, role, full_name, created_at) VALUES (?, ?, ?, ?, ?, ?)")
        .bind(&admin_user.id)
        .bind(&admin_user.email)
        .bind(&admin_user.password_hash)
        .bind(&admin_user.role)
        .bind(&admin_user.full_name)
        .bind(&admin_user.created_at)
        .execute(db)
        .await?;

    tracing::info!("Created default admin user: <EMAIL>");
    Ok(())
}
