use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use validator::Validate;

// User roles as defined in CMS.md
#[derive(Serialize, Deserialize, Clone, Debug, PartialEq)]
pub enum UserRole {
    Ad<PERSON>,
    Teacher,
    Parent,
    Student,
}

impl UserRole {
    pub fn as_str(&self) -> &'static str {
        match self {
            UserRole::Admin => "admin",
            UserRole::Teacher => "teacher",
            UserRole::Parent => "parent",
            UserRole::Student => "student",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "admin" => Some(UserRole::Admin),
            "teacher" => Some(UserRole::Teacher),
            "parent" => Some(UserRole::Parent),
            "student" => Some(UserRole::Student),
            _ => None,
        }
    }
}

// User model matching D1.users table
#[derive(Serialize, Deserialize, Clone, Debug, sqlx::FromRow)]
pub struct User {
    pub id: String,
    pub email: String,
    pub password_hash: String,
    pub role: String, // Store as string for database compatibility
    pub full_name: String,
    pub created_at: DateTime<Utc>,
}

impl User {
    pub fn new(email: String, password_hash: String, role: UserRole, full_name: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            email,
            password_hash,
            role: role.as_str().to_string(),
            full_name,
            created_at: Utc::now(),
        }
    }

    pub fn get_role(&self) -> Option<UserRole> {
        UserRole::from_str(&self.role)
    }
}

#[derive(Serialize, Deserialize, Clone, Debug, sqlx::FromRow)]
pub struct Session {
    pub id: String,
    pub user_id: String,
    pub jwt_token: String,
    pub expires_at: DateTime<Utc>,
}

impl Session {
    pub fn new(user_id: String, jwt_token: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            user_id,
            jwt_token,
            expires_at: Utc::now() + chrono::Duration::hours(24),
        }
    }
}

#[derive(Serialize, Deserialize, Clone)]
pub struct AuditLog {
    pub id: String,
    pub user_id: String,
    pub action: String,
    pub resource_id: Option<String>,
    pub created_at: DateTime<Utc>,
}

impl AuditLog {
    pub fn new(user_id: String, action: String, resource_id: Option<String>) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            user_id,
            action,
            resource_id,
            created_at: Utc::now(),
        }
    }
}

#[derive(Serialize, Deserialize, Clone)]
pub struct BlogPost {
    pub id: String,
    pub slug: String,
    pub title: String,
    pub content: String,
    pub excerpt: String,
    pub author_id: String,
    pub tags: Vec<String>,
    pub visibility: String, // "public" or "private"
    pub cover_image: Option<String>,
    pub inline_images: Vec<String>,
    pub attachments: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl BlogPost {
    pub fn new(
        title: String,
        content: String,
        excerpt: String,
        author_id: String,
        tags: Vec<String>,
        visibility: String,
        cover_image: Option<String>,
        inline_images: Vec<String>,
        attachments: Vec<String>,
    ) -> Self {
        let slug = title.to_lowercase()
            .replace(" ", "-")
            .chars()
            .map(|c| if c.is_alphanumeric() || c == '-' { c } else { '-' })
            .collect::<String>()
            .trim_matches('-')
            .to_string();

        Self {
            id: Uuid::new_v4().to_string(),
            slug,
            title,
            content,
            excerpt,
            author_id,
            tags,
            visibility,
            cover_image,
            inline_images,
            attachments,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    pub fn update(&mut self, title: String, content: String, excerpt: String, tags: Vec<String>, visibility: String, cover_image: Option<String>, inline_images: Vec<String>, attachments: Vec<String>) {
        self.title = title;
        self.content = content;
        self.excerpt = excerpt;
        self.tags = tags;
        self.visibility = visibility;
        self.cover_image = cover_image;
        self.inline_images = inline_images;
        self.attachments = attachments;
        self.updated_at = Utc::now();
    }
}

// Request/Response DTOs
#[derive(Deserialize)]
pub struct LoginRequest {
    pub email: String,
    pub password: String,
}

#[derive(Serialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: UserResponse,
    pub expires_at: usize, // Unix timestamp for client-side expiry checks
}

#[derive(Serialize, Clone)]
pub struct UserResponse {
    pub id: String,
    pub email: String,
    pub role: String,
    pub full_name: String,
}

#[derive(Deserialize)]
pub struct CreateBlogPostRequest {
    pub title: String,
    pub summary: Option<String>,
    pub body_html: String,
    pub tags: Vec<String>,
    pub visibility: String,
    pub cover_image: Option<String>,
    pub attachments: Vec<String>,
}

#[derive(Serialize)]
pub struct BlogPostResponse {
    pub id: String,
    pub slug: String,
    pub title: String,
    pub content: String,
    pub excerpt: String,
    pub author: UserResponse,
    pub tags: Vec<String>,
    pub visibility: String,
    pub cover_image: Option<String>,
    pub inline_images: Vec<String>,
    pub attachments: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,
    pub exp: usize,
}
