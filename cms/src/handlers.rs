use axum::{
    extract::{Path, State, Extension},
    http::{StatusCode, HeaderMap, header},
    response::{<PERSON><PERSON>, Response, IntoResponse},
    routing::{get, post, put, delete},
    middleware,
    Router,
};
use tower_http::cors::Cors<PERSON>ayer;

use crate::error::{AppError, AppR<PERSON>ult};
use crate::models::{
    CreateBlogPostRequest, LoginRequest,
    LoginResponse, UserResponse,
};
use crate::kv::{BlogPostKv, BlogIndexEntry};
use crate::blog::BlogService;
use crate::auth::AuthService;
use crate::middleware::{auth_middleware, admin_middleware, AuthUser};
use crate::AppState;

pub fn create_router(state: AppState) -> Router {
    // Create admin routes with authentication middleware
    let admin_routes = Router::new()
        .route("/api/admin/posts", get(admin_list_posts).post(admin_create_post))
        .route("/api/admin/posts/{slug}", get(admin_get_post).put(admin_update_post).delete(admin_delete_post))
        .layer(middleware::from_fn(admin_middleware))
        .layer(middleware::from_fn_with_state(state.clone(), auth_middleware));

    // Create protected routes with authentication middleware
    let protected_routes = Router::new()
        .route("/api/users/me", get(verify_session))
        .layer(middleware::from_fn_with_state(state.clone(), auth_middleware));

    Router::new()
        .route("/", get(root))
        .route("/healthz", get(health_check))
        // Auth routes (no middleware needed)
        .route("/api/auth/login", post(login))
        .route("/api/auth/logout", post(logout))
        // Public blog endpoints for SvelteKit SSR (no middleware needed)
        .route("/api/blog/index", get(get_blog_index))
        .route("/api/blog/post/{slug}", get(get_public_blog_post))
        // Merge protected routes
        .merge(admin_routes)
        .merge(protected_routes)
        .layer(
            CorsLayer::new()
                .allow_origin([
                    "https://llacademy.ng".parse::<axum::http::HeaderValue>().unwrap(),
                    "https://www.llacademy.ng".parse::<axum::http::HeaderValue>().unwrap(),
                    "http://localhost:5173".parse::<axum::http::HeaderValue>().unwrap(), // For development
                ])
                .allow_methods([
                    axum::http::Method::GET,
                    axum::http::Method::POST,
                    axum::http::Method::PUT,
                    axum::http::Method::DELETE,
                    axum::http::Method::OPTIONS,
                ])
                .allow_headers([
                    header::CONTENT_TYPE,
                    header::AUTHORIZATION,
                    header::ACCEPT,
                    header::COOKIE,
                    header::SET_COOKIE,
                ])
                .allow_credentials(true)
        )
        .with_state(state)
}

async fn root() -> &'static str {
    "LLA Web CMS API"
}

async fn health_check() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "service": "LLA Web CMS"
    }))
}

async fn login(
    State(state): State<AppState>,
    Json(payload): Json<LoginRequest>,
) -> AppResult<impl IntoResponse> {
    let auth_service = AuthService::new(state.db.clone(), state.config.clone());
    let (response, cookie_value) = auth_service.login(payload).await?;
    
    let mut headers = HeaderMap::new();
    headers.insert(header::SET_COOKIE, cookie_value.parse().unwrap());
    
    Ok((headers, Json(response)))
}

async fn logout(
    State(state): State<AppState>,
    headers: HeaderMap,
) -> AppResult<impl IntoResponse> {
    let auth_service = AuthService::new(state.db.clone(), state.config.clone());
    
    // Extract session cookie
    if let Some(cookie_header) = headers.get(header::COOKIE) {
        if let Ok(cookie_str) = cookie_header.to_str() {
            // Parse session cookie
            for cookie in cookie_str.split(';') {
                let cookie = cookie.trim();
                if cookie.starts_with("session=") {
                    let session_value = &cookie[8..];
                    let clear_cookie = auth_service.logout_with_cookie(session_value).await?;
                    
                    let mut response_headers = HeaderMap::new();
                    response_headers.insert(header::SET_COOKIE, clear_cookie.parse().unwrap());
                    
                    return Ok((response_headers, StatusCode::NO_CONTENT));
                }
            }
        }
    }
    
    Ok((HeaderMap::new(), StatusCode::NO_CONTENT))
}

// Legacy handlers - these should be removed and replaced with admin handlers

// Admin handlers (protected routes)
async fn admin_list_posts(
    State(state): State<AppState>,
    Extension(user): Extension<UserResponse>,
) -> AppResult<Json<Vec<BlogIndexEntry>>> {
    // User is already verified by middleware
    let blog_service = BlogService::new(state.kv.clone(), state.db.clone());
    let posts = blog_service.list_posts(true).await?; // Include private posts
    Ok(Json(posts))
}

async fn admin_create_post(
    State(state): State<AppState>,
    Extension(user): Extension<UserResponse>,
    Json(payload): Json<CreateBlogPostRequest>,
) -> AppResult<Json<BlogPostKv>> {
    let blog_service = BlogService::new(state.kv.clone(), state.db.clone());
    let blog_post = blog_service.create_post(payload, user.id.clone()).await?;
    Ok(Json(blog_post))
}

async fn admin_get_post(
    State(state): State<AppState>,
    Path(slug): Path<String>,
    Extension(user): Extension<UserResponse>,
) -> AppResult<Json<BlogPostKv>> {
    // User is already verified by middleware
    let blog_service = BlogService::new(state.kv.clone(), state.db.clone());
    let blog_post = blog_service.get_post(&slug).await?
        .ok_or_else(|| AppError::NotFound("Blog post not found".to_string()))?;

    Ok(Json(blog_post))
}

async fn admin_update_post(
    State(state): State<AppState>,
    Path(slug): Path<String>,
    Extension(user): Extension<UserResponse>,
    Json(payload): Json<CreateBlogPostRequest>,
) -> AppResult<Json<BlogPostKv>> {
    let blog_service = BlogService::new(state.kv.clone(), state.db.clone());
    let blog_post = blog_service.update_post(&slug, payload, user.id.clone()).await?;
    Ok(Json(blog_post))
}

async fn admin_delete_post(
    State(state): State<AppState>,
    Path(slug): Path<String>,
    Extension(user): Extension<UserResponse>,
) -> AppResult<StatusCode> {
    let blog_service = BlogService::new(state.kv.clone(), state.db.clone());
    blog_service.delete_post(&slug, user.id.clone()).await?;
    Ok(StatusCode::NO_CONTENT)
}

// Session verification - user is already verified by middleware
async fn verify_session(
    Extension(user): Extension<UserResponse>,
) -> AppResult<Json<UserResponse>> {
    Ok(Json(user))
}

// Public blog endpoints for SvelteKit SSR
async fn get_blog_index(State(state): State<AppState>) -> AppResult<Json<Vec<BlogIndexEntry>>> {
    let blog_service = BlogService::new(state.kv.clone(), state.db.clone());
    let public_posts = blog_service.list_posts(false).await?; // Only public posts
    Ok(Json(public_posts))
}

async fn get_public_blog_post(
    State(state): State<AppState>,
    Path(slug): Path<String>,
) -> AppResult<Json<BlogPostKv>> {
    let blog_service = BlogService::new(state.kv.clone(), state.db.clone());
    let blog_post = blog_service.get_post(&slug).await?
        .ok_or_else(|| AppError::NotFound("Blog post not found".to_string()))?;

    // Check if post is public
    if blog_post.visibility != "public" {
        return Err(AppError::NotFound("Blog post not found".to_string()));
    }

    Ok(Json(blog_post))
}
