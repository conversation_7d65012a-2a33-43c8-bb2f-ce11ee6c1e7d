use crate::config::AppConfig;
use crate::error::{AppError, AppResult};
use crate::models::{Claims, Revocation, User, UserResponse, UserRole};
use chrono::Utc;
use sqlx::SqlitePool;
use uuid::Uuid;

pub struct AuthService {
    pub db: SqlitePool,
    pub config: AppConfig,
}

impl AuthService {
    pub fn new(db: SqlitePool, config: AppConfig) -> Self {
        Self { db, config }
    }

    /// Create JWT token with revocation support
    pub async fn create_jwt_token(&self, user_id: &str) -> AppResult<String> {
        let jti = Uuid::new_v4().to_string();
        let now = Utc::now();
        let exp = now + chrono::Duration::days(7);

        let claims = Claims {
            sub: user_id.to_string(),
            exp: exp.timestamp() as usize,
            jti: jti.clone(),
            iat: now.timestamp() as usize,
        };

        let token = jsonwebtoken::encode(
            &jsonwebtoken::Header::default(),
            &claims,
            &jsonwebtoken::EncodingKey::from_secret(self.config.jwt_secret.as_ref()),
        )?;

        Ok(token)
    }

    /// Verify JWT token and check revocation list
    pub async fn verify_jwt_token(&self, token: &str) -> AppResult<Claims> {
        // Decode JWT token
        let token_data = jsonwebtoken::decode::<Claims>(
            token,
            &jsonwebtoken::DecodingKey::from_secret(self.config.jwt_secret.as_ref()),
            &jsonwebtoken::Validation::default(),
        )
        .map_err(|_| AppError::Auth("Invalid token".to_string()))?;

        // Check if token is revoked
        let revoked: Option<String> =
            sqlx::query_scalar("SELECT jti FROM revocations WHERE jti = $1")
                .bind(&token_data.claims.jti)
                .fetch_optional(&self.db)
                .await?;

        if revoked.is_some() {
            return Err(AppError::Auth("Token has been revoked".to_string()));
        }

        Ok(token_data.claims)
    }

    /// Revoke a JWT token
    pub async fn revoke_token(&self, token: &str) -> AppResult<()> {
        let claims = self.verify_jwt_token(token).await?;

        let revocation = Revocation::new(
            claims.jti,
            Some(claims.sub),
            Some(chrono::DateTime::from_timestamp(claims.exp as i64, 0).unwrap_or(Utc::now())),
        );

        sqlx::query("INSERT INTO revocations (jti, user_id, revoked_at, expires_at) VALUES ($1, $2, $3, $4)")
            .bind(&revocation.jti)
            .bind(&revocation.user_id)
            .bind(&revocation.revoked_at)
            .bind(&revocation.expires_at)
            .execute(&self.db)
            .await?;

        Ok(())
    }

    /// Get user by email (for authentication)
    pub async fn get_user_by_email(&self, email: &str) -> AppResult<Option<User>> {
        let user: Option<User> = sqlx::query_as(
            "SELECT id, email, role, google_id, full_name, created_at FROM users WHERE email = $1",
        )
        .bind(email)
        .fetch_optional(&self.db)
        .await?;
        Ok(user)
    }

    /// Get user by Google ID
    pub async fn get_user_by_google_id(&self, google_id: &str) -> AppResult<Option<User>> {
        let user: Option<User> = sqlx::query_as(
            "SELECT id, email, role, google_id, full_name, created_at FROM users WHERE google_id = $1"
        )
        .bind(google_id)
        .fetch_optional(&self.db)
        .await?;
        Ok(user)
    }

    /// Create a new user with Google authentication
    pub async fn create_user_with_google(
        &self,
        email: String,
        google_id: String,
        full_name: Option<String>,
    ) -> AppResult<User> {
        let user = User::new_with_google(email, UserRole::Student, google_id, full_name);

        sqlx::query("INSERT INTO users (id, email, role, google_id, full_name, created_at) VALUES ($1, $2, $3, $4, $5, $6)")
            .bind(&user.id)
            .bind(&user.email)
            .bind(&user.role)
            .bind(&user.google_id)
            .bind(&user.full_name)
            .bind(&user.created_at)
            .execute(&self.db)
            .await?;

        Ok(user)
    }

    /// Verify session cookie and return user info
    pub async fn verify_session_cookie(&self, cookie_value: &str) -> AppResult<UserResponse> {
        let claims = self.verify_jwt_token(cookie_value).await?;

        // Get user details
        let user: Option<User> = sqlx::query_as(
            "SELECT id, email, role, google_id, full_name, created_at FROM users WHERE id = $1",
        )
        .bind(&claims.sub)
        .fetch_optional(&self.db)
        .await?;

        let user = user.ok_or_else(|| AppError::Auth("User not found".to_string()))?;

        Ok(UserResponse {
            id: user.id,
            email: user.email,
            role: user.role,
            full_name: user.full_name,
        })
    }

    /// Create cookie string for JWT token
    pub fn create_cookie_string(&self, token: &str) -> String {
        if self.config.environment == "development" {
            format!(
                "session={}; Path=/; HttpOnly; SameSite=Lax; Max-Age={}",
                token,
                7 * 24 * 60 * 60 // 7 days in seconds
            )
        } else {
            format!(
                "session={}; Domain=.llacademy.ng; Path=/; Secure; HttpOnly; SameSite=None; Max-Age={}",
                token,
                7 * 24 * 60 * 60 // 7 days in seconds
            )
        }
    }

    /// Create cookie string for logout (clears the cookie)
    pub fn create_logout_cookie_string(&self) -> String {
        if self.config.environment == "development" {
            "session=; Path=/; HttpOnly; SameSite=Lax; Max-Age=0".to_string()
        } else {
            "session=; Domain=.llacademy.ng; Path=/; Secure; HttpOnly; SameSite=None; Max-Age=0"
                .to_string()
        }
    }

    /// Logout by revoking the token
    pub async fn logout_with_cookie(&self, cookie_value: &str) -> AppResult<String> {
        // Revoke the token
        if let Err(_) = self.revoke_token(cookie_value).await {
            // If token is already invalid, that's fine for logout
        }

        // Return cookie clearing string
        Ok(self.create_logout_cookie_string())
    }
}
