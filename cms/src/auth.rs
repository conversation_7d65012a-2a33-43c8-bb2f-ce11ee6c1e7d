use chrono::Utc;
use sqlx::SqlitePool;
use axum::http::{StatusCode, HeaderMap, header};
use axum::response::{Response, IntoResponse};
use crate::error::{AppError, AppResult};
use crate::models::{User, Session, Claims, AuditLog, LoginRequest, LoginResponse, UserResponse};
use crate::config::AppConfig;

pub struct AuthService {
    pub db: SqlitePool,
    pub config: AppConfig,
}

impl AuthService {
    pub fn new(db: SqlitePool, config: AppConfig) -> Self {
        Self { db, config }
    }

    pub async fn login(&self, payload: LoginRequest) -> AppResult<(LoginResponse, String)> {
        // Find user by email
        let user: Option<User> = sqlx::query_as(
            "SELECT id, email, password_hash, role, full_name, created_at FROM users WHERE email = $1"
        )
        .bind(&payload.email)
        .fetch_optional(&self.db)
        .await?;

        let user = user.ok_or_else(|| AppError::Auth("Invalid credentials".to_string()))?;

        // Verify password
        if !bcrypt::verify(&payload.password, &user.password_hash)? {
            return Err(AppError::Auth("Invalid credentials".to_string()));
        }

        // Create JWT token with longer expiration for session cookies
        let claims = Claims {
            sub: user.id.clone(),
            exp: (Utc::now() + chrono::Duration::days(7)).timestamp() as usize, // 7 days for cookie
        };

        let token = jsonwebtoken::encode(
            &jsonwebtoken::Header::default(),
            &claims,
            &jsonwebtoken::EncodingKey::from_secret(self.config.jwt_secret.as_ref()),
        )?;

        // Create session in database
        let session = Session::new(user.id.clone(), token.clone());
        sqlx::query("INSERT INTO sessions (id, user_id, jwt_token, expires_at) VALUES ($1, $2, $3, $4)")
            .bind(&session.id)
            .bind(&session.user_id)
            .bind(&session.jwt_token)
            .bind(&session.expires_at)
            .execute(&self.db)
            .await?;

        // Log audit event
        self.log_audit(&user.id, "login", None).await?;

        let user_response = UserResponse {
            id: user.id,
            email: user.email,
            role: user.role,
            full_name: user.full_name,
        };

        // Create HttpOnly cookie string with environment-appropriate settings
        let cookie_value = if self.config.environment == "development" {
            format!(
                "session={}; Path=/; HttpOnly; SameSite=Lax; Max-Age={}",
                token,
                7 * 24 * 60 * 60 // 7 days in seconds
            )
        } else {
            format!(
                "session={}; Domain=.llacademy.ng; Path=/; Secure; HttpOnly; SameSite=None; Max-Age={}",
                token,
                7 * 24 * 60 * 60 // 7 days in seconds
            )
        };

        Ok((LoginResponse {
            token: token.clone(), // Still return token for compatibility
            user: user_response,
            expires_at: claims.exp, // Add expiry timestamp for client-side checks
        }, cookie_value))
    }

    pub async fn logout(&self, session_token: &str) -> AppResult<()> {
        // Invalidate session
        sqlx::query("DELETE FROM sessions WHERE jwt_token = $1")
            .bind(session_token)
            .execute(&self.db)
            .await?;

        Ok(())
    }

    pub async fn verify_session_token(&self, token: &str) -> AppResult<UserResponse> {
        // Verify JWT token
        let claims = jsonwebtoken::decode::<Claims>(
            token,
            &jsonwebtoken::DecodingKey::from_secret(self.config.jwt_secret.as_ref()),
            &jsonwebtoken::Validation::default(),
        )
        .map_err(|_| AppError::Auth("Invalid token".to_string()))?;

        // Check if session exists in database
        let session: Option<Session> = sqlx::query_as(
            "SELECT id, user_id, jwt_token, expires_at FROM sessions WHERE jwt_token = $1 AND expires_at > $2"
        )
        .bind(token)
        .bind(Utc::now())
        .fetch_optional(&self.db)
        .await?;

        let session = session.ok_or_else(|| AppError::Auth("Session not found or expired".to_string()))?;

        // Get user details
        let user: Option<User> = sqlx::query_as(
            "SELECT id, email, password_hash, role, full_name, created_at FROM users WHERE id = $1"
        )
        .bind(&session.user_id)
        .fetch_optional(&self.db)
        .await?;

        let user = user.ok_or_else(|| AppError::Auth("User not found".to_string()))?;

        Ok(UserResponse {
            id: user.id,
            email: user.email,
            role: user.role,
            full_name: user.full_name,
        })
    }

    pub async fn verify_session_cookie(&self, cookie_value: &str) -> AppResult<UserResponse> {
        self.verify_session_token(cookie_value).await
    }

    pub async fn get_user_by_email(&self, email: &str) -> AppResult<Option<User>> {
        let user: Option<User> = sqlx::query_as(
            "SELECT id, email, password_hash, role, full_name, created_at FROM users WHERE email = $1"
        )
        .bind(email)
        .fetch_optional(&self.db)
        .await?;
        Ok(user)
    }

    pub async fn logout_with_cookie(&self, cookie_value: &str) -> AppResult<String> {
        // Invalidate session
        sqlx::query("DELETE FROM sessions WHERE jwt_token = $1")
            .bind(cookie_value)
            .execute(&self.db)
            .await?;

        // Return cookie clearing string with environment-appropriate settings
        let clear_cookie = if self.config.environment == "development" {
            "session=; Path=/; HttpOnly; SameSite=Lax; Max-Age=0".to_string()
        } else {
            "session=; Domain=.llacademy.ng; Path=/; Secure; HttpOnly; SameSite=None; Max-Age=0".to_string()
        };

        Ok(clear_cookie)
    }

    async fn log_audit(&self, user_id: &str, action: &str, resource_id: Option<String>) -> AppResult<()> {
        let audit_log = AuditLog::new(user_id.to_string(), action.to_string(), resource_id);
        sqlx::query("INSERT INTO audit_logs (id, user_id, action, resource_id, created_at) VALUES ($1, $2, $3, $4, $5)")
            .bind(&audit_log.id)
            .bind(&audit_log.user_id)
            .bind(&audit_log.action)
            .bind(&audit_log.resource_id)
            .bind(&audit_log.created_at)
            .execute(&self.db)
            .await?;
        Ok(())
    }
}
