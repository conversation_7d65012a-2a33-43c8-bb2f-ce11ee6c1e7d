use crate::audit::AuditService;
use crate::error::{AppError, AppResult};
use crate::kv::{BlogIndexEntry, BlogPostKv, KvStore};
use crate::models::{CreateBlogPostRequest, User};
use chrono::Utc;
use sqlx::SqlitePool;
use uuid::Uuid;

pub struct BlogService {
    pub kv: KvStore,
    pub db: SqlitePool,
    pub audit: AuditService,
}

impl BlogService {
    pub fn new(kv: KvStore, db: SqlitePool) -> Self {
        let audit = AuditService::new(db.clone());
        Self { kv, db, audit }
    }

    pub async fn create_post(
        &self,
        payload: CreateBlogPostRequest,
        author_id: String,
    ) -> AppResult<BlogPostKv> {
        // Create slug from title
        let slug = self.create_slug(&payload.title);

        // Create blog post for KV storage
        let blog_post = BlogPostKv {
            id: Uuid::new_v4().to_string(),
            title: payload.title,
            slug: slug.clone(),
            summary: payload.summary.unwrap_or_default(),
            body_html: payload.body_html,
            author_id: author_id.clone(),
            tags: payload.tags,
            date_published: Utc::now().to_rfc3339(),
            visibility: payload.visibility,
            cover_image: payload.cover_image,
            attachments: payload.attachments,
            meta: None,
        };

        // Store in KV
        self.kv.put_blog_post(&slug, &blog_post).await?;

        // Log audit event
        self.log_audit(&author_id, "create_blog_post", Some(blog_post.id.clone()))
            .await?;

        Ok(blog_post)
    }

    pub async fn get_post(&self, slug: &str) -> AppResult<Option<BlogPostKv>> {
        Ok(self.kv.get_blog_post(slug).await?)
    }

    pub async fn get_public_post(&self, slug: &str) -> AppResult<Option<BlogPostKv>> {
        let blog_post = self.kv.get_blog_post(slug).await?;

        match blog_post {
            Some(post) if post.visibility == "public" => Ok(Some(post)),
            Some(_) => Ok(None), // Private post, return None
            None => Ok(None),
        }
    }

    pub async fn update_post(
        &self,
        slug: &str,
        payload: CreateBlogPostRequest,
        author_id: String,
    ) -> AppResult<BlogPostKv> {
        let mut blog_post = self
            .kv
            .get_blog_post(slug)
            .await?
            .ok_or_else(|| AppError::NotFound("Blog post not found".to_string()))?;

        // Update blog post fields
        blog_post.title = payload.title;
        blog_post.summary = payload.summary.unwrap_or_default();
        blog_post.body_html = payload.body_html;
        blog_post.tags = payload.tags;
        blog_post.visibility = payload.visibility;
        blog_post.cover_image = payload.cover_image;
        blog_post.attachments = payload.attachments;

        // Store updated post in KV
        self.kv.put_blog_post(slug, &blog_post).await?;

        // Log audit event
        self.log_audit(&author_id, "update_blog_post", Some(blog_post.id.clone()))
            .await?;

        Ok(blog_post)
    }

    pub async fn delete_post(&self, slug: &str, author_id: String) -> AppResult<BlogPostKv> {
        let blog_post = self
            .kv
            .get_blog_post(slug)
            .await?
            .ok_or_else(|| AppError::NotFound("Blog post not found".to_string()))?;

        // Delete from KV
        self.kv.delete_blog_post(slug).await?;

        // Log audit event
        self.log_audit(&author_id, "delete_blog_post", Some(blog_post.id.clone()))
            .await?;

        Ok(blog_post)
    }

    pub async fn list_posts(&self, include_private: bool) -> AppResult<Vec<BlogIndexEntry>> {
        let blog_index = self.kv.get_blog_index().await?;

        if include_private {
            Ok(blog_index)
        } else {
            // Filter out private posts for public access
            let public_posts: Vec<BlogIndexEntry> = blog_index
                .into_iter()
                .filter(|post| post.visibility == "public")
                .collect();
            Ok(public_posts)
        }
    }

    pub async fn get_user(&self, user_id: &str) -> AppResult<Option<User>> {
        let user: Option<User> = sqlx::query_as(
            "SELECT id, email, password_hash, role, full_name, created_at FROM users WHERE id = $1",
        )
        .bind(user_id)
        .fetch_optional(&self.db)
        .await?;
        Ok(user)
    }

    pub async fn get_user_by_email(&self, email: &str) -> AppResult<Option<User>> {
        let user: Option<User> = sqlx::query_as("SELECT id, email, password_hash, role, full_name, created_at FROM users WHERE email = $1")
            .bind(email)
            .fetch_optional(&self.db)
            .await?;
        Ok(user)
    }

    fn create_slug(&self, title: &str) -> String {
        title
            .to_lowercase()
            .replace(" ", "-")
            .chars()
            .map(|c| {
                if c.is_alphanumeric() || c == '-' {
                    c
                } else {
                    '-'
                }
            })
            .collect::<String>()
            .trim_matches('-')
            .to_string()
    }

    async fn log_audit(
        &self,
        user_id: &str,
        action: &str,
        resource_id: Option<String>,
    ) -> AppResult<()> {
        self.audit
            .log_action(user_id, action.to_string(), resource_id, None)
            .await
    }
}
