[package]
name = "llaweb-cms"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "llaweb-cms"
path = "src/main.rs"

[dependencies]
anyhow = "1.0.99"
axum = { version = "0.8.4", features = ["multipart"] }
bcrypt = "0.17.1"
chrono = { version = "0.4.41", features = ["serde"] }
config = "0.15.15"
jsonwebtoken = "9.3.1"
regex = "1.11.2"
reqwest = { version = "0.12.23", features = ["json", "multipart"] }
serde = "1.0.219"
serde_json = "1.0.143"
sqlx = { version = "0.8", features = [
    "runtime-tokio",
    "sqlite",
    "chrono",
    "uuid",
    "macros",
] }
thiserror = "2.0.16"
tokio = { version = "1.47.1", features = ["full"] }
tower = "0.5.2"
tower-http = { version = "0.6.6", features = ["cors"] }
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.20", features = ["env-filter"] }
uuid = { version = "1.18.0", features = ["v4", "serde"] }
validator = { version = "0.18", features = ["derive"] }
