[package]
name = "llaweb-cms"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "llaweb-cms"
path = "src/main.rs"

[dependencies]
anyhow = "1.0.99"
axum = { version = "0.8.4", features = ["multipart"] }
bcrypt = "0.17.1"
chrono = { version = "0.4.41", features = ["serde"] }
config = "0.15.15"
jsonwebtoken = "9.3.1"
regex = "1.11.2"
reqwest = { version = "0.12.23", features = ["json", "multipart"] }
serde = "1.0.219"
serde_json = "1.0.143"
sqlx = { version = "0.8", features = [
    "runtime-tokio",
    "sqlite",
    "chrono",
    "uuid",
    "macros",
    "json",
] }
thiserror = "2.0.16"
tokio = { version = "1.47.1", features = ["full"] }
tower = "0.5.2"
tower-http = { version = "0.6.6", features = ["cors"] }
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.20", features = ["env-filter"] }
uuid = { version = "1.18.0", features = ["v4", "serde"] }
validator = { version = "0.18", features = ["derive"] }
# New dependencies for Google Auth and SharePoint integration
oauth2 = "4.4.2"
base64 = "0.22.1"
url = "2.5.0"
# For SharePoint/MS Graph API integration
graph-rs-sdk = "3.0.0"
# For backup compression
flate2 = "1.0.35"
# For scheduled tasks
tokio-cron-scheduler = "0.13.0"

[dev-dependencies]
tempfile = "3.8.1"
tokio-test = "0.4.3"
