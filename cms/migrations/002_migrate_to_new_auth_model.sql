-- Migration to new authentication model with JWT + revocation list
-- Following the ticket requirements for passwordless login + Google Auth

-- Drop the old sessions table as we're moving to JWT with revocation list
DROP TABLE IF EXISTS sessions;

-- Update users table to support <PERSON> Auth and remove password requirement
-- First create a new table with the updated schema
CREATE TABLE users_new (
    id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('admin', 'teacher', 'parent', 'student')),
    google_id TEXT UNIQUE, -- For Google OAuth integration
    full_name TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Copy existing users data (excluding password_hash)
INSERT INTO users_new (id, email, role, full_name, created_at)
SELECT id, email, role, full_name, created_at FROM users;

-- Drop old users table and rename new one
DROP TABLE users;
ALTER TABLE users_new RENAME TO users;

-- Create revocations table for JWT blacklist
CREATE TABLE revocations (
    jti TEXT PRIMARY KEY, -- JWT ID for revoked tokens
    user_id TEXT,
    revoked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME, -- When the revoked token would have expired
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create audit logs table for current month (September 2025)
-- Following monthly sharding strategy from the ticket
CREATE TABLE audit_logs_2025_09 (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    session_date TEXT NOT NULL, -- Format: YYYY-MM-DD for grouping actions by day
    actions TEXT NOT NULL DEFAULT '[]', -- JSON array of actions
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Drop the old audit_logs table
DROP TABLE IF EXISTS audit_logs;

-- Create indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_google_id ON users(google_id);
CREATE INDEX idx_revocations_jti ON revocations(jti);
CREATE INDEX idx_revocations_user_id ON revocations(user_id);
CREATE INDEX idx_revocations_expires_at ON revocations(expires_at);
CREATE INDEX idx_audit_logs_2025_09_user_id ON audit_logs_2025_09(user_id);
CREATE INDEX idx_audit_logs_2025_09_session_date ON audit_logs_2025_09(session_date);
CREATE INDEX idx_audit_logs_2025_09_created_at ON audit_logs_2025_09(created_at);

-- Drop the roles table as it's not needed with the simplified enum approach
DROP TABLE IF EXISTS roles;
