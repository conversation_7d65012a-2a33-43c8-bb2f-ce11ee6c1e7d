# Migration Guide: D1 to SQLite with WAL Mode

This guide documents the migration from Cloudflare D1 to local SQLite with WAL mode optimizations, JWT authentication with revocation lists, and incremental JSON audit logging.

## Overview of Changes

### 1. Database Migration
- **From**: Cloudflare D1 (remote database)
- **To**: Local SQLite with WAL mode and performance optimizations
- **Benefits**: 
  - No quota limits
  - Better performance on NVMe storage
  - Full control over database operations
  - Simplified architecture

### 2. Authentication System
- **From**: Session-based authentication with database sessions
- **To**: JWT tokens with revocation list
- **Benefits**:
  - Stateless authentication
  - Better scalability
  - Support for Google OAuth
  - Reduced database reads for auth verification

### 3. Audit Logging
- **From**: Row-per-action audit logs
- **To**: Incremental JSON audit logging with monthly sharding
- **Benefits**:
  - Reduced database writes
  - Better performance for high-activity users
  - Automatic row rotation to prevent large JSON objects
  - Monthly table sharding for easier maintenance

### 4. Backup System
- **New**: Automated SQLite backups to SharePoint Drive
- **Features**:
  - Daily compressed backups
  - Automatic cleanup of old backups
  - SharePoint integration via MS Graph API
  - Local backup retention for disaster recovery

## Database Configuration

### SQLite WAL Mode Settings
The following pragmas are automatically applied:

```sql
PRAGMA journal_mode = WAL;          -- Enable WAL mode
PRAGMA synchronous = NORMAL;        -- Balance safety and performance
PRAGMA cache_size = -65536;         -- 64MB page cache
PRAGMA temp_store = MEMORY;         -- Store temp tables in memory
PRAGMA mmap_size = 268435456;       -- 256MB memory-mapped I/O
```

### Performance Optimizations
- **NVMe Storage**: Leverages sequential write performance
- **Memory Mapping**: Reduces I/O for frequently accessed data
- **Large Page Cache**: Improves query performance
- **WAL Mode**: Allows concurrent readers with single writer

## Authentication Changes

### JWT Token Structure
```json
{
  "sub": "user_id",           // User ID
  "exp": 1234567890,          // Expiration timestamp
  "jti": "unique_token_id",   // JWT ID for revocation
  "iat": 1234567890           // Issued at timestamp
}
```

### Revocation System
- Revoked tokens are stored in the `revocations` table
- Token verification checks both JWT validity and revocation status
- Automatic cleanup of expired revocations

### Google OAuth Integration
- Users can authenticate via Google OAuth
- User records include `google_id` field
- Supports both email/password and Google authentication

## Audit Logging System

### JSON Structure
Each audit log row contains:
```json
{
  "id": "log_id",
  "user_id": "user_id", 
  "session_date": "2025-09-03",
  "actions": [
    {
      "timestamp": "2025-09-03T10:30:00Z",
      "action": "login",
      "resource_id": null,
      "details": null
    },
    {
      "timestamp": "2025-09-03T10:31:00Z", 
      "action": "create_post",
      "resource_id": "post_123",
      "details": {"title": "New Post"}
    }
  ]
}
```

### Row Rotation Rules
- New row created when:
  - JSON exceeds 1MB in size
  - Action count exceeds 50 per row
  - New day begins

### Monthly Sharding
- Tables named: `audit_logs_YYYY_MM`
- Automatic table creation
- Keeps last 3 months in SQLite
- Older logs can be exported to SharePoint

### Querying Audit Logs
```rust
// Get user audit logs for date range
let logs = audit_service.get_user_audit_logs(
    "user_id",
    start_date,
    end_date
).await?;

// Log a new action
audit_service.log_action(
    "user_id",
    "action_name".to_string(),
    Some("resource_id".to_string()),
    Some(serde_json::json!({"key": "value"}))
).await?;
```

## Backup System

### Configuration
Set these environment variables:
```bash
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# SharePoint configuration
SHAREPOINT_TENANT_ID=your_tenant_id
SHAREPOINT_CLIENT_ID=your_client_id  
SHAREPOINT_CLIENT_SECRET=your_client_secret
SHAREPOINT_SITE_ID=your_site_id
SHAREPOINT_DRIVE_ID=your_drive_id
```

### Backup Process
1. **VACUUM INTO**: Creates clean backup file
2. **Compression**: Gzip compression to reduce size
3. **Upload**: Uploads to SharePoint Drive via MS Graph API
4. **Cleanup**: Removes local files after successful upload
5. **Retention**: Automatically removes old backups

### Manual Backup
```rust
let backup_service = BackupService::new(db, config);
backup_service.backup_database().await?;
```

### Recovery Process
1. Stop the application
2. Download backup from SharePoint
3. Decompress the backup file
4. Replace the current database file
5. Restart the application

## Migration Steps

### 1. Update Dependencies
The following dependencies were added:
- `oauth2` - Google OAuth integration
- `graph-rs-sdk` - SharePoint integration  
- `flate2` - Backup compression
- `tokio-cron-scheduler` - Scheduled backups

### 2. Database Schema Migration
Run the migration:
```bash
sqlx migrate run
```

This will:
- Remove the `sessions` table
- Update `users` table schema
- Create `revocations` table
- Create current month's audit table

### 3. Configuration Updates
Update your configuration with new fields:
- Google OAuth settings
- SharePoint settings  
- Backup settings

### 4. Code Changes
- Update authentication to use JWT tokens
- Replace audit logging calls
- Configure backup scheduler

## Monitoring and Maintenance

### Database Maintenance
- **WAL Checkpointing**: Automatic via SQLite
- **VACUUM**: Run periodically to reclaim space
- **ANALYZE**: Update query planner statistics

### Audit Log Maintenance  
- **Monthly Cleanup**: Automatic table creation/cleanup
- **Size Monitoring**: Monitor JSON row sizes
- **Export**: Export old logs before deletion

### Backup Monitoring
- **Success/Failure Logs**: Check application logs
- **SharePoint Storage**: Monitor storage usage
- **Recovery Testing**: Periodically test backup recovery

## Troubleshooting

### Common Issues

**JWT Token Issues**:
- Check JWT secret configuration
- Verify token expiration times
- Check revocation table for unexpected entries

**Audit Log Issues**:
- Verify JSON structure in audit tables
- Check table creation permissions
- Monitor row rotation behavior

**Backup Issues**:
- Verify SharePoint credentials
- Check network connectivity
- Monitor disk space for local backups

**Performance Issues**:
- Check WAL file size
- Monitor cache hit ratios
- Verify NVMe storage performance

### Useful Queries

```sql
-- Check WAL mode status
PRAGMA journal_mode;

-- Check cache statistics  
PRAGMA cache_size;

-- View current audit tables
SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'audit_logs_%';

-- Check revoked tokens
SELECT COUNT(*) FROM revocations WHERE expires_at > datetime('now');
```

## Security Considerations

- **JWT Secret**: Use strong, randomly generated secrets
- **Token Expiration**: Set appropriate expiration times
- **Revocation Cleanup**: Regularly clean expired revocations
- **Backup Encryption**: Consider encrypting backups at rest
- **Access Control**: Limit database file permissions
- **SharePoint Security**: Use least-privilege service accounts
