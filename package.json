{"name": "llaweb", "version": "0.0.1", "devDependencies": {"@sveltejs/adapter-cloudflare": "^7.2.3", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "svelte": "^4.2.7", "svelte-check": "^4.0.0", "typescript": "^5.0.0", "vite": "^5.0.3"}, "dependencies": {"@cloudflare/workers-types": "^4.20240129.0"}, "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "deploy": "npm run build && wrangler pages deploy", "wrangler": "wrangler"}, "type": "module"}