# Architecture Overview — components (Updated 2025)

* **Frontend**: SvelteKit app deployed to **Cloudflare Pages (Hybrid SSR + CSR)**
* **Backend / CMS**: Rust app (Dokploy on VPS) serving APIs at `school.llacademy.ng`
* **Storage & Edge services (Cloudflare)**: KV (`BLOG_KV`), D1 (`school_auth`), Cloudflare Images, R2 (`school-media`)
* **Domains**:

  * `llacademy.ng`, `www.llacademy.ng` → SvelteKit (Pages)
  * `school.llacademy.ng` → Rust API (proxied by Cloudflare)
  * `media.llacademy.ng` → Images & R2 CDN (fronted by Cloudflare)
* **Secrets & Config**: Cloudflare environment variables (via API or dashboard), Vault for CI, GitHub Actions secrets (or similar)

## Performance Optimization (New)
* **Authentication Strategy**: Hybrid approach to avoid Cloudflare execution time limits
* **Private Areas**: CSR with direct API authentication for optimal performance
* **Public Content**: SSR with edge caching for SEO and performance

---

# 1. High-level pipeline (end-to-end)

1. **Admin login & content creation**

   * Teacher logs into Rust CMS (auth via D1).
   * Upload assets: Images → Cloudflare Images; Big files → R2.
   * CMS composes final post JSON (body\_html, metadata, asset URLs, visibility).
   * CMS writes post to KV (`blog:post:{slug}`) and updates `blog:index`.
   * CMS writes audit entry into D1.

2. **Public site rendering**

   * User hits `llacademy.ng/blog` or `/blog/{slug}`.
   * SvelteKit SSR (Pages function) reads `blog:index` or `blog:post:{slug}` from KV.
   * Private posts are filtered out at the API level - no server-side session verification in SvelteKit.
   * SvelteKit renders page, referencing asset URLs on `media.llacademy.ng`.

3. **Private area access (New CSR approach)**

   * User accesses `/portal/*` or `/admin/*` routes.
   * Client-side authentication check using localStorage + API verification.
   * Direct API calls to `school.llacademy.ng` with HttpOnly cookies via `credentials: 'include'`.
   * No server-side authentication in SvelteKit hooks - optimized for performance.

3. **Preview / Drafts**

   * CMS pushes draft posts to a `drafts` KV namespace or stores drafts in D1 (with access controls).
   * Preview URLs include signed tokens (short TTL) that SvelteKit verifies server-side.

4. **Secrets & rotation**

   * CMS pushes rotating webhook secrets or keys into Cloudflare env via API.
   * CI/CD tokens stored in secure secrets management (not in repo).

---

# 2. SvelteKit-side: Implementation details & best practices

## Structure & routing

* Routes: `src/routes`

  * `/` (prerendered)
  * `/about`, `/admissions`, `/contact`, `/academics`, `/portfolio`, `/robots.txt`, `/sitemap.xml` (prerendered)
  * `/blog` (SSR; reads `blog:index` from KV)
  * `/blog/[slug]` (SSR; reads `blog:post:{slug}` from KV; server-only logic)
  * `/portal/*` (CSR; requires authenticated session checked against D1/Rust API)

## svelte.config.js

* Use `@sveltejs/adapter-cloudflare` with `routes.exclude: ['/blog/*']` if you want to force SSR for blog, but in this design SvelteKit will SSR blog pages reading KV.
* Bindings configured for `BLOG_KV`, `D1` (if you'd like direct D1 bindings), and any env secrets.

## Server-side fetching & caching pattern

* `/blog` load():

  * `const index = await platform.env.BLOG_KV.get('blog:index');`
  * Parse and render list.
  * Set cache-control headers on the SSR response: `public, max-age=60, s-maxage=3600` (short local, long edge).
* `/blog/[slug]` load():

  * `const post = await BLOG_KV.get('blog:post:{slug}');`
  * **Private posts are filtered at the API level** - no server-side session verification in SvelteKit for performance.
  * Return `post` into page and render `{@html post.body_html}`.

## Hook: handle() / global cache control (Updated for Performance)

* Implement simplified `src/hooks.server.ts` handle to:

  * **NO authentication parsing** - to avoid Cloudflare execution time limits.
  * **Only** set Cache-Control headers for public blog pages.
  * **Security headers** for all requests (X-Content-Type-Options, X-Frame-Options, etc.).
  * **Private areas use CSR** - authentication handled client-side with direct API calls.

## Caching strategy (edge + origin)

* KV reads are fast; still set `Cache-Control`:

  * Public posts: `Cache-Control: public, max-age=60, s-maxage=3600`
  * Private posts: `Cache-Control: private, no-store`
* Let Cloudflare’s CDN serve cached HTML for anonymous users. Use `stale-while-revalidate` patterns if needed.

## Authentication (frontend)

* **Hybrid authentication strategy for performance optimization:**
  * **Public content**: No authentication required in SvelteKit - optimized for speed
  * **Private areas**: CSR authentication using localStorage + API verification
* Use secure HttpOnly cookie set by Rust API for portal sessions (cookie domain `.llacademy.ng`, `SameSite=None; Secure; HttpOnly`).
* For client-side API calls (from browser to `school.llacademy.ng`) use `fetch(..., { credentials: 'include' })`.
* Authentication state managed in client-side layouts (e.g., `/portal/admin/+layout.svelte`).

## Preview mode & drafts

* CMS writes drafts to `KV` with key `draft:blog:post:{slug}:{preview_token}`, or hold in D1.
* Preview routes use CSR authentication pattern for performance - token validation done via API calls to Rust backend.

## Error handling

* If KV read fails: return 503 with friendly maintenance page.
* If post not found: 404 template.
* Log errors (discussed in observability).

---

# 3. Rust CMS-side: Implementation details & best practices 
* Directory: `cms/src/main.rs`
## Core responsibilities

* Admin UI & API (auth, uploads, post management)
* Image uploads → Cloudflare Images API
* File uploads → R2 (signed PUT URL or server-side PUT)
* KV writes for posts & index
* D1 writes for users, sessions, audit logs

## API design (REST endpoints)

* `POST /api/auth/login` → returns secure cookie (HttpOnly) or JWT
* `POST /api/auth/logout` → clears session
* `GET /api/admin/posts` → admin-only list (from D1 or combined)
* `POST /api/admin/posts` → create post: uploads images/R2 → write KV
* `PUT /api/admin/posts/{slug}` → update post: re-upload assets, update KV
* `DELETE /api/admin/posts/{slug}` → delete KV key + remove assets (or mark deleted)
* `POST /webhook/push` → optional if CMS receives content from external systems
* `GET /api/users/me` → verify session/cookie

## Auth & sessions (D1)

* Passwords hashed with bcrypt/argon2.
* Sessions: store JWT or session id in D1 `sessions` table with expiration.
* Session cookie attributes:

  * `Set-Cookie: session=xxx; Domain=.llacademy.ng; Path=/; Secure; HttpOnly; SameSite=None; Max-Age=...`
* Token rotation & revocation: store token id in sessions for quick revocation.

## Uploading images & files

* **Images**: upload to Cloudflare Images via API, record returned image id/URL in KV post JSON. Images can be created client-side (direct) using signed URLs from CMS or server-side upload through CMS (simpler).
* **R2**: store large assets using authenticated PUTs; prefer uploading from CMS backend to R2 and return CDN URL to include in post.

## Publishing flow inside CMS

1. Upload images/files → get URLs
2. Render or sanitize post HTML
3. Compose JSON: `{id, title, slug, body_html, tags, author_id, date_published, visibility, cover_image, attachments}`
4. PUT to KV: `blog:post:{slug}` and update `blog:index` (atomic-like: write `blog:post:{slug}` then `blog:index`).
5. Write audit log into D1.

## Safety: sanitization

* Always sanitize `body_html` on server (strip unsafe tags, attributes) before saving to KV.
* Consider storing original Markdown and rendering to HTML in CMS controlled renderer.

---

# 4. Data models (detailed)

## KV (BLOG\_KV)

* Key: `blog:post:{slug}`

  * Value JSON:

    * `id` (uuid)
    * `title` (string)
    * `slug` (string)
    * `summary` (string)
    * `body_html` (string)
    * `author_id` (uuid)
    * `tags` (array)
    * `date_published` (ISO8601)
    * `visibility` (`public`|`private`)
    * `cover_image` (image id or URL)
    * `attachments` (array of R2 URLs)
    * `meta` (optional extra metadata)
* Key: `blog:index` — JSON array of `{slug, title, summary, cover_image, date_published, tags}` sorted desc

## D1 schema (as earlier — users, sessions, audit\_logs)

* `users`: id, email, password\_hash, role, full\_name, created\_at, updated\_at
* `sessions`: id, user\_id, jwt\_token/session\_token, expires\_at, created\_at
* `audit_logs`: id, user\_id, action, resource\_id, details, created\_at

---

# 5. Naming conventions (concise)

* Slugs: `lowercase-words-separated-by-dashes`
* KV keys:

  * Posts: `blog:post:{slug}`
  * Index: `blog:index`
  * Drafts: `blog:draft:{slug}:{token}`
* Images (Cloudflare Images): `blog-{slug}-cover`, `blog-{slug}-img-{n}` (map ID to KV)
* R2 objects: `reports/{year}/{slug}.pdf`, `events/{year}/{slug}.mp4`
* D1 GUIDs: lowercase UUID v4
* Timestamps: ISO 8601 UTC (`2025-09-01T12:00:00Z`)

---

# 6. Security & networking

## Cookies & CORS

* Cookie: `Domain=.llacademy.ng; Path=/; Secure; HttpOnly; SameSite=None`
* API: `Access-Control-Allow-Origin: https://llacademy.ng` (not `*`) and `Access-Control-Allow-Credentials: true`
* Cloudflare rules: bypass caching for `/api/*` (Cache Rule: `school.llacademy.ng/api/*` → Cache Level: Bypass)
* TLS: Cloudflare SSL settings: **Full (Strict)**; keep Dokploy TLS cert valid.

## Cloudflare firewall & DDoS

* Apply WAF rules to `school.llacademy.ng` (block known bad IPs/SQLi or XSS patterns).
* Rate limit login endpoints `POST /api/auth/login` (e.g., 5 req/min per IP).
* Create IP allow/deny lists for admin endpoints if desired.

## Secrets

* Store Cloudflare API tokens in a secure vault; for automation use short-lived tokens if possible.
* CMS must have an API token with minimal scope: only KV write + Images/R2 write as required.
* Rotate secrets periodically and provide rotation endpoint in CMS to push new env to Cloudflare.

---

# 7. Observability, logging & backups

## Logging & metrics

* Rust CMS logs: structured JSON logs (timestamp, level, request\_id, user\_id, route, duration).
* SvelteKit errors: capture stack traces server-side, forward critical errors to a monitoring system.
* Use a log aggregator (e.g., Logflare / remote ELK / Datadog) for retention & search.
* Instrument key metrics: KV read latency, page response time, D1 read/write counts, Images/R2 bandwidth usage.

## Health checks

* `/healthz` on Rust API returning DB connectivity and disk space; configure Cloudflare uptime checks or external monitors.

## Backups

* D1: export DB snapshots daily (scheduled).
* R2: lifecycle backup or replicate to another storage (optional).
* KV: export keys or use your CMS to keep authoritative copy (posts can be re-sourced from CMS).

---

# 8. Failure modes & mitigations

| Failure                       |                             Likely cause | Mitigation                                                                           |
| ----------------------------- | ---------------------------------------: | ------------------------------------------------------------------------------------ |
| KV read fails                 | Cloudflare incident or binding misconfig | Return cached static page fallback; show maintenance message; alert ops              |
| KV eventual consistency delay |                  Write propagation delay | CMS can wait 1–2s before confirming publish; show “publishing” state                 |
| D1 outage                     |                   Cloudflare D1 incident | Only public posts accessible; fallback to read-only mode; queue writes               |
| Image upload failure          |                           Network or API | Retry with backoff; keep local copy and retry; notify admin                          |
| Exceeded KV writes (1k/day)   |                  Bulk content publishing | Throttle publishes; batch index updates; use staging -> bulk write in off-peak hours |
| Token compromise              |                             API key leak | Rotate tokens; revoke old ones; add IP restrictions to tokens                        |

---

# 9. Scalability & cost-control tips

* Cache aggressively at edge for public pages.
* Use `s-maxage` / `stale-while-revalidate` to trade latency for freshness.
* Avoid SSR where static pre-render works.
* Use Cloudflare Images to reduce bandwidth & transformation costs.
* Monitor usage (KV reads/writes, D1 queries, R2 egress) and set budget alerts.

---

# 10. CI/CD & deployment workflow

## SvelteKit (Pages)

* GitHub repo → Cloudflare Pages (connect via repo)
* Build command: `npm run build`
* Build output: automatically configured for Cloudflare adapter (or `.svelte-kit/cloudflare`)
* Use preview branches sparingly (free tier build limits). Batch updates in one PR when possible.

## Rust CMS

* GitHub Actions → deploy to VPS via Dokploy (or container registry + Dokploy)
* Keep release tags; perform DB migrations with migration tool (run migrations in CI/CD step)
* During deploy: warm KV or pre-populate frequently-read content if needed.

## Secrets in CI

* Store Cloudflare API tokens and DB credentials in GitHub Secrets.
* Use least-privilege tokens; rotate regularly.

---

# 11. Operational runbook (short)

* **Publish failure**: check CMS logs → KV PUT status. Retry writes and notify staff.
* **KV read 404 for existing post**: check `blog:post:{slug}` via `wrangler` or Cloudflare API. Check propagation delay.
* **Image broken**: verify R2/Images URL; confirm CDN mapping `media.llacademy.ng` is set and cert valid.
* **Auth issues**: check D1 connectivity, session table, and cookie attributes (`Domain`, `Secure`, `SameSite`).
* **High latency**: identify origin vs CDN; check KV latency; check D1 slow queries.

---

# 12. Appendix — Quick reference snippets (conceptual, no code)

* KV keys: `blog:post:back-to-school-2025`, `blog:index`, `blog:draft:...`
* D1 tables: `users`, `sessions`, `audit_logs`
* Images names: `blog-back-to-school-2025-cover`
* R2 paths: `reports/2025/back-to-school.pdf`
* Cookie header: `Set-Cookie: session=...; Domain=.llacademy.ng; Secure; HttpOnly; SameSite=None; Max-Age=...`
